import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { MediaModule } from '@modules/data/media/media.module';
import { UrlModule } from '@modules/data/url/url.module';
import { MarketplaceModule } from '@modules/marketplace/marketplace.module';
import { ModelTrainingModule } from '@modules/model-training/model-training.module';
import { ApiKeyEncryptionHelper } from '@modules/model-training/helpers/api-key-encryption.helper';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { S3Service } from '@shared/services/s3.service';
import { ToolsModule } from '@/modules/tools/tools.module';
import { ToolsUserModule } from '@/modules/tools/user/tools-user.module';
import { AdminGroupToolRepository, UserGroupToolRepository, UserGroupToolsTypeAgentRepository } from '@/modules/tools/repositories';
import { StrategyUserModule } from '@modules/strategy/user/strategy-user.module';
import { BusinessUserModule } from '@modules/business/user/business-user.module';
import { IntegrationModule } from '@modules/integration/integration.module';
import { UserWebsiteRepository, FacebookPageRepository } from '@modules/integration/repositories';
import {
  Agent,
  AgentMedia,
  AgentProduct,
  AgentUrl,
  AgentUser,
  AdminGroupToolsTypeAgents,
  TypeAgent
} from '@modules/agent/entities';
import { UserGroupTool, UserGroupToolsTypeAgent } from '@modules/tools/entities';
import {
  AgentRepository,
  AgentUserRepository,
  TypeAgentRepository,
  AgentMediaRepository,
  AgentUrlRepository,
  AgentProductRepository
} from '@modules/agent/repositories';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { UserProviderModelRepository } from '@modules/model-training/repositories/user-provider-model.repository';
import {
  TypeAgentUserController,
  AgentUserController,
  AgentResourceUserController,
  AgentStrategyUserController,
  AgentOutputController
} from './controllers';
import {
  TypeAgentUserService,
  AgentUserService,
  AgentResourceUserService,
  AgentStrategyService,
  AgentOutputService
} from './services';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserProduct,
      Agent,
      AgentUser,
      AgentMedia,
      AgentUrl,
      AgentProduct,
      TypeAgent,
      AdminGroupToolsTypeAgents,
      UserGroupToolsTypeAgent,
      UserGroupTool
    ]),
    HttpModule,
    MediaModule,
    UrlModule,
    MarketplaceModule,
    ToolsModule,
    ToolsUserModule,
    ModelTrainingModule,
    KnowledgeFilesModule,
    ConfigModule,
    StrategyUserModule,
    BusinessUserModule,
    IntegrationModule,
  ],
  controllers: [
    TypeAgentUserController,
    AgentUserController,
    AgentResourceUserController,
    AgentStrategyUserController,
    AgentOutputController,
  ],
  providers: [
    // Services
    TypeAgentUserService,
    AgentUserService,
    AgentResourceUserService,
    AgentStrategyService,
    AgentOutputService,

    // External services
    OpenAiService,
    S3Service,
    ApiKeyEncryptionHelper,
    RagFileProcessingService,

    // Repositories
    TypeAgentRepository,
    AgentRepository,
    AgentUserRepository,
    AgentMediaRepository,
    AgentUrlRepository,
    AgentProductRepository,
    VectorStoreRepository,
    UserProviderModelRepository,
    AdminGroupToolRepository,
    UserGroupToolRepository,
    UserGroupToolsTypeAgentRepository,
    MediaRepository,
    UrlRepository,
    UserWebsiteRepository,
    FacebookPageRepository,
  ],
  exports: [
    TypeAgentUserService,
    AgentUserService,
    AgentResourceUserService,
    AgentStrategyService,
    AgentOutputService,
  ],
})
export class AgentUserModule {}
