import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO để xóa nhiều file
 */
export class DeleteFilesDto {
  @ApiProperty({
    description: 'Danh sách ID của các file cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray({ message: 'fileIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một file ID' })
  @IsNumber({}, { each: true, message: 'Mỗi file ID phải là số' })
  @IsNotEmpty({ each: true, message: 'File ID không được để trống' })
  @Type(() => Number)
  fileIds: number[];
}
