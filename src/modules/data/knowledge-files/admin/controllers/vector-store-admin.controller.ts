import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { VectorStoreAdminService } from '../services';
import {
  CreateVectorStoreDto,
  QueryVectorStoreDto,
  AssignFilesDto,
  DeleteVectorStoresDto,
  UpdateVectorStoreDto,
  VectorStoreResponseDto,
} from '../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../../exceptions';
import { Roles } from '@modules/auth/decorators/roles.decorator';

/**
 * Controller xử lý API liên quan đến quản lý vector store cho admin
 * Cung cấp các endpoint để tạo, xem danh sách, xem chi tiết, gán file và xóa vector store
 * Admin có thể quản lý tất cả các vector store trong hệ thống
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_KNOWLEDGE_FILES)
@ApiExtraModels(
  ApiResponseDto,
  VectorStoreResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
  CreateVectorStoreDto,
  AssignFilesDto,
  DeleteVectorStoresDto,
  UpdateVectorStoreDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/vector-stores')
export class VectorStoreAdminController {
  constructor(
    private readonly vectorStoreAdminService: VectorStoreAdminService,
  ) {}

  /**
   * Tạo vector store mới
   * Tạo một vector store mới với tên được cung cấp
   * Vector store được sử dụng để lưu trữ và tìm kiếm các file tri thức
   * Mỗi vector store có thể chứa nhiều file tri thức
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param createVectorStoreDto DTO chứa thông tin vector store cần tạo
   * @returns Thông tin vector store đã tạo
   */
  @ApiOperation({
    summary: 'Tạo vector store mới',
    description: 'Tạo một vector store mới với tên được cung cấp'
  })
  @ApiBody({
    type: CreateVectorStoreDto,
    description: 'Thông tin vector store cần tạo',
    examples: {
      'Basic': {
        value: {
          name: 'VectorStoreAI'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR
  )

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async createVectorStore(
    @Body() createVectorStoreDto: CreateVectorStoreDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<VectorStoreResponseDto>> {
    const result = await this.vectorStoreAdminService.createVectorStore(
      createVectorStoreDto,
      employeeId,
    );
    return ApiResponseDto.created(
      result,
      'Tạo vector store thành công.'
    );
  }

  /**
   * Lấy danh sách vector stores với phân trang, tìm kiếm và sắp xếp
   * Hỗ trợ các tham số query:
   * - search: Từ khóa tìm kiếm (tìm theo tên vector store)
   * - page: Số trang (bắt đầu từ 1)
   * - limit: Số lượng kết quả trên một trang
   * - sortBy: Sắp xếp theo trường (name, storage, createdAt)
   * - sortDirection: Thứ tự sắp xếp (asc, desc)
   * - ownerType: Lọc theo loại chủ sở hữu (USER hoặc ADMIN)
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách vector store phân trang
   */
  @ApiOperation({
    summary: 'Lấy danh sách vector stores',
    description: 'Lấy danh sách vector stores với phân trang, tìm kiếm và sắp xếp'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách vector store thành công.',
    schema: ApiResponseDto.getPaginatedSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR
  )

  @Get()
  @HttpCode(HttpStatus.OK)
  async getVectorStores(
    @Query() queryDto: QueryVectorStoreDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<PaginatedResult<VectorStoreResponseDto>>> {
    const result = await this.vectorStoreAdminService.getVectorStores(
      queryDto,
      employeeId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách vector store thành công.'
    );
  }

  /**
   * Cập nhật thông tin vector store
   * Cập nhật tên và dung lượng của vector store
   * Admin có thể cập nhật thông tin của tất cả các vector store mà họ sở hữu
   *
   * @param id ID của vector store cần cập nhật
   * @param updateVectorStoreDto DTO chứa thông tin cập nhật
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @returns Thông tin vector store đã cập nhật
   */
  @ApiOperation({
    summary: 'Cập nhật thông tin vector store',
    description: 'Cập nhật tên và dung lượng của vector store'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiBody({
    type: UpdateVectorStoreDto,
    description: 'Thông tin cập nhật cho vector store',
    examples: {
      'Basic': {
        value: {
          name: 'Vector Store Mới',
          storage: 2048
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_UPDATE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR
  )
  @Put(':id')
  @HttpCode(HttpStatus.OK)
  async updateVectorStore(
    @Param('id') id: string,
    @Body() updateVectorStoreDto: UpdateVectorStoreDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<VectorStoreResponseDto>> {
    const result = await this.vectorStoreAdminService.updateVectorStore(
      id,
      updateVectorStoreDto,
      employeeId,
    );
    return ApiResponseDto.success(
      result,
      'Cập nhật vector store thành công.'
    );
  }

  /**
   * Lấy thông tin chi tiết vector store theo ID
   * Trả về thông tin chi tiết của vector store bao gồm tên, dung lượng, số lượng file, số lượng agent, v.v.
   * Admin có thể xem thông tin chi tiết của tất cả các vector store
   *
   * @param id ID của vector store cần xem thông tin
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @returns Thông tin chi tiết vector store
   */
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết vector store',
    description: 'Lấy thông tin chi tiết của vector store bao gồm tên, dung lượng, số lượng file, số lượng agent, v.v.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin chi tiết vector store thành công.',
    schema: ApiResponseDto.getSchema(VectorStoreResponseDto)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR
  )

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  async getVectorStoreDetail(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<VectorStoreResponseDto>> {
    const result = await this.vectorStoreAdminService.getVectorStoreDetail(
      id,
      employeeId,
    );
    return ApiResponseDto.success(
      result,
      'Lấy thông tin chi tiết vector store thành công.'
    );
  }

  /**
   * Gán file vào vector store và xử lý với RAG API
   * Gán một hoặc nhiều file tri thức vào vector store
   * File được gán vào vector store sẽ được xử lý bởi RAG API và sử dụng để tìm kiếm và trả lời câu hỏi
   * Mỗi file có thể được gán vào nhiều vector store khác nhau
   *
   * @param id ID của vector store
   * @param assignFilesDto DTO chứa danh sách ID của các file cần gán
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @returns Thông tin về việc gán file thành công, bao gồm số lượng file đã gán và danh sách file bỏ qua
   */
  @ApiOperation({
    summary: 'Gán file vào vector store và xử lý với RAG API',
    description: 'Gán một hoặc nhiều file tri thức vào vector store và xử lý bởi RAG API để sử dụng cho việc tìm kiếm và trả lời câu hỏi'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiBody({
    type: AssignFilesDto,
    description: 'Danh sách ID của các file cần gán và thông số xử lý chunk',
    examples: {
      'Basic': {
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ]
        }
      },
      'WithChunkSettings': {
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ],
          chunkSize: 2000,
          chunkOverlap: 100
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Đã nhận file ID từ RAG API và gán vào vector store thành công.',
    schema: ApiResponseDto.getSchema(Object)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_ASSIGN_FILES_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR
  )


  @Post(':id/files')
  @HttpCode(HttpStatus.OK)
  async assignFilesToVectorStore(
    @Param('id') id: string,
    @Body() assignFilesDto: AssignFilesDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{success: boolean; message: string; processedFiles?: number; skippedFiles?: string[]}>> {
    const result = await this.vectorStoreAdminService.assignFilesToVectorStore(
      id,
      assignFilesDto,
      employeeId,
    );
    return ApiResponseDto.success(
      result,
      'Đã nhận file ID từ RAG API và gán vào vector store thành công.'
    );
  }

  /**
   * Xóa nhiều file khỏi vector store
   * Xóa nhiều file tri thức khỏi vector store cùng lúc
   * Các file sau khi xóa khỏi vector store sẽ không được sử dụng để tìm kiếm và trả lời câu hỏi
   * Các file vẫn tồn tại trong hệ thống và có thể được gán lại vào vector store sau này
   *
   * @param id ID của vector store
   * @param assignFilesDto DTO chứa danh sách ID của các file cần xóa
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @returns Thông tin về việc xóa file thành công
   */
  @ApiOperation({
    summary: 'Xóa nhiều file khỏi vector store',
    description: 'Xóa nhiều file tri thức khỏi vector store cùng lúc, các file vẫn tồn tại trong hệ thống'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vector store',
    example: 'vs_a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @ApiBody({
    type: AssignFilesDto,
    description: 'Danh sách ID của các file cần xóa',
    examples: {
      'Basic': {
        value: {
          fileIds: [
            'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
            'b2c3d4e5-f6a7-8901-bcde-f01234567890'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file khỏi vector store thành công.',
    schema: ApiResponseDto.getSchema(Object)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_REMOVE_FILES_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR
  )

  @Delete(':id/files')
  @HttpCode(HttpStatus.OK)
  async removeFilesFromVectorStore(
    @Param('id') id: string,
    @Body() assignFilesDto: AssignFilesDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{success: boolean; message: string}>> {
    const result = await this.vectorStoreAdminService.removeFilesFromVectorStore(
      id,
      assignFilesDto.fileIds,
      employeeId,
    );
    return ApiResponseDto.success(
      result,
      'Xóa file khỏi vector store thành công.'
    );
  }

  /**
   * Xóa nhiều vector store
   * Xóa một hoặc nhiều vector store cùng lúc
   * Các file trong vector store sẽ không bị xóa, chỉ xóa liên kết giữa file và vector store
   * Các agent sử dụng vector store này sẽ không còn truy cập được vào dữ liệu trong vector store
   *
   * @param deleteVectorStoresDto DTO chứa danh sách ID của các vector store cần xóa
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @returns Thông tin về việc xóa vector store thành công, bao gồm số lượng vector store đã xóa và danh sách vector store thất bại
   */
  @ApiOperation({
    summary: 'Xóa nhiều vector store',
    description: 'Xóa một hoặc nhiều vector store cùng lúc, các file trong vector store sẽ không bị xóa'
  })
  @ApiBody({ type: DeleteVectorStoresDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa vector store thành công.',
    schema: ApiResponseDto.getSchema(Object)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_PERMISSION_ERROR
  )
  @Delete()
  @HttpCode(HttpStatus.OK)
  async deleteVectorStores(
    @Body() deleteVectorStoresDto: DeleteVectorStoresDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{success: boolean; deletedCount: number; failedItems?: {id: string; reason: string}[]}>> {
    const result = await this.vectorStoreAdminService.deleteVectorStore(
      deleteVectorStoresDto.storeIds,
      employeeId,
    );
    return ApiResponseDto.success(
      result,
      `Đã xóa ${result.deletedCount} vector store thành công.`
    );
  }
}