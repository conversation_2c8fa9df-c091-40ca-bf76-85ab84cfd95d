import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import {
  KnowledgeFileAdminController,
  VectorStoreAdminController,
} from './controllers';
import { KnowledgeFileAdminService, VectorStoreAdminService } from './services';
import { KnowledgeFile, VectorStore, VectorStoreFile } from '../entities';
import {
  KnowledgeFileRepository,
  VectorStoreRepository,
  VectorStoreFileRepository,
} from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { ValidationHelper } from '../helpers/validation.helper';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeFile, VectorStore, VectorStoreFile]),
    HttpModule,
  ],
  controllers: [KnowledgeFileAdminController, VectorStoreAdminController],
  providers: [
    KnowledgeFileAdminService,
    VectorStoreAdminService,
    KnowledgeFileRepository,
    VectorStoreRepository,
    VectorStoreFileRepository,
    S3Service,
    ValidationHelper,
    RagFileProcessingService,
  ],
  exports: [KnowledgeFileAdminService, VectorStoreAdminService],
})
export class KnowledgeFilesAdminModule {}
