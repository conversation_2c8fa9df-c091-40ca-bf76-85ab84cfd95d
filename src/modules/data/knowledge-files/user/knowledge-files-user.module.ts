import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { KnowledgeFileUserController, VectorStoreUserController } from './controllers';
import { KnowledgeFileUserService, VectorStoreUserService } from './services';
import { KnowledgeFile, VectorStore, VectorStoreFile } from '../entities';
import { KnowledgeFileRepository, VectorStoreRepository, VectorStoreFileRepository } from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { KnowledgeFileUserValidationHelper } from './helpers';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([KnowledgeFile, VectorStore, VectorStoreFile]),
    HttpModule,
  ],
  controllers: [KnowledgeFileUserController, VectorStoreUserController],
  providers: [
    KnowledgeFileUserService,
    VectorStoreUserService,
    KnowledgeFileRepository,
    VectorStoreRepository,
    VectorStoreFileRepository,
    KnowledgeFileUserValidationHelper,
    S3Service,
    RagFileProcessingService
  ],
  exports: [KnowledgeFileUserService, VectorStoreUserService],
})
export class KnowledgeFilesUserModule {}
