import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import {
  KnowledgeFileRepository,
  VectorStoreFileRepository,
  VectorStoreRepository,
} from '../../repositories';
import {
  AssignFilesDto,
  AssignFilesResponseDto,
  CreateVectorStoreDto,
  ProcessedFileDetailDto,
  QueryVectorStoreDto,
  UpdateVectorStoreDto,
  VectorStoreResponseDto,
} from '../dto';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response/api-response-dto';
import { OwnerType } from '@shared/enums';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions';
import { KnowledgeFileUserValidationHelper } from '../helpers';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { Transactional } from 'typeorm-transactional';
import { v4 as uuidv4 } from 'uuid';
@Injectable()
export class VectorStoreUserService {
  private readonly logger = new Logger(VectorStoreUserService.name);

  constructor(
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly vectorStoreFileRepository: VectorStoreFileRepository,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly dataSource: DataSource,
    private readonly validationHelper: KnowledgeFileUserValidationHelper,
    private readonly ragFileProcessingService: RagFileProcessingService,
  ) {}

  /**
   * Tạo một vector store mới
   * @param dto Thông tin vector store cần tạo
   * @param userId ID của người dùng
   * @returns Thông tin vector store đã tạo
   */
  @Transactional()
  async createVectorStore(
    dto: CreateVectorStoreDto,
    userId: number,
  ): Promise<VectorStoreResponseDto> {
    try {
      this.logger.log(`Bắt đầu tạo vector store cho user ID ${userId} với tên: ${dto.name}`);

      // Tạo ID duy nhất cho vector store
      const vectorStoreId = `vs_${uuidv4()}`;
      this.logger.log(`Đã tạo ID cho vector store: ${vectorStoreId}`);

      // Tạo bản ghi vector store
      const vectorStore = this.vectorStoreRepository.create({
        id: vectorStoreId,
        name: dto.name,
        storage: 0,
        ownerType: OwnerType.USER,
        ownerId: userId,
        createdAt: Date.now(),
        updateAt: Date.now(),
      });

      // Lưu vào database
      this.logger.log(`Đang lưu vector store vào database với ID: ${vectorStore.id}`);
      await this.vectorStoreRepository.save(vectorStore);
      this.logger.log(`Đã lưu vector store vào database thành công`);

      // Trả về thông tin vector store
      return plainToInstance(
        VectorStoreResponseDto,
        {
          storeId: vectorStore.id,
          storeName: vectorStore.name,
          size: vectorStore.storage,
          agents: 0,
          files: 0,
          createdAt: vectorStore.createdAt,
          updatedAt: vectorStore.updateAt,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(
        `Error creating vector store: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi tạo vector store: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách vector store của người dùng
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách vector store với phân trang
   */
  async getVectorStores(
    queryDto: QueryVectorStoreDto,
    userId: number,
  ): Promise<PaginatedResult<VectorStoreResponseDto>> {
    try {
      // Sử dụng repository method để lấy danh sách vector store
      const result =
        await this.vectorStoreRepository.findAllByUserIdWithPagination(
          queryDto,
          userId,
        );
      const vectorStores = result.items;

      // Lấy số lượng file và agent trong mỗi vector store
      const storeIds = vectorStores.map((store) => store.id);

      // Chỉ truy vấn khi có vector stores
      const fileCountMap =
        await this.vectorStoreRepository.countFilesInMultipleVectorStores(
          storeIds,
        );
      const agentCountMap =
        await this.vectorStoreRepository.countAgentsInMultipleVectorStores(
          storeIds,
        );

      // Chuyển đổi sang DTO
      const vectorStoreResponses = vectorStores.map((store) => {
        return plainToInstance(
          VectorStoreResponseDto,
          {
            storeId: store.id,
            storeName: store.name,
            size: store.storage,
            agents: agentCountMap.get(store.id) || 0,
            files: fileCountMap.get(store.id) || 0,
            createdAt: store.createdAt,
            updatedAt: store.updateAt,
          },
          { excludeExtraneousValues: true },
        );
      });

      // Trả về kết quả phân trang
      return {
        items: vectorStoreResponses,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting vector stores: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy danh sách vector store: ${error.message}`,
      );
    }
  }

  /**
   * Gán các file vào vector store
   * @param vectorStoreId ID của vector store
   * @param dto Thông tin các file cần gán
   * @param userId ID của người dùng
   * @returns Thông tin về việc gán file thành công, bao gồm số lượng file đã xử lý và danh sách file bị bỏ qua
   */
  @Transactional()
  async assignFilesToVectorStore(
    vectorStoreId: string,
    dto: AssignFilesDto,
    userId: number,
  ): Promise<AssignFilesResponseDto> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Kiểm tra các file có tồn tại và thuộc về người dùng không
      const files = await this.knowledgeFileRepository.find({
        where: {
          id: In(dto.fileIds),
          ownedBy: userId,
          ownerType: OwnerType.USER,
        },
      });

      // Xác thực các file tồn tại
      this.validationHelper.validateFilesExist(files, dto.fileIds);

      // Kiểm tra xem file đã được gán vào vector store nào chưa
      const existingVectorStoreFiles =
        await this.vectorStoreFileRepository.find({
          where: { fileId: In(dto.fileIds) },
        });

      // Lấy thông tin chi tiết của các file
      const filesDetails = await this.knowledgeFileRepository.find({
        where: {
          id: In(dto.fileIds),
          ownedBy: userId,
          ownerType: OwnerType.USER,
        },
      });

      // Lọc ra các file cần xử lý: chưa được gán vào vector store hiện tại hoặc đã gán nhưng chưa có fileId
      const filesToAssign = dto.fileIds.filter((fileId) => {
        const existingAssignment = existingVectorStoreFiles.find(
          (vsf) => vsf.fileId === fileId && vsf.vectorStoreId === vectorStoreId
        );

        // Nếu file chưa được gán vào vector store hiện tại, cần xử lý
        if (!existingAssignment) {
          return true;
        }

        // Nếu file đã được gán vào vector store hiện tại, kiểm tra xem có fileId chưa
        const fileDetail = filesDetails.find(file => file.id === fileId);
        return fileDetail && !fileDetail.fileId; // Cần xử lý nếu file chưa có fileId
      });

      // Nếu không có file nào cần xử lý, trả về kết quả thành công
      if (filesToAssign.length === 0) {
        return {
          success: true,
          message: 'Tất cả các file đã được gán vào vector store này.',
          processedFiles: 0,
          processedFileDetails: [],
          skippedFiles: [],
        };
      }

      // Lọc ra các bản ghi liên kết cần xóa (chỉ xóa các bản ghi liên kết với vector store khác)
      const vectorStoreFilesToRemove = existingVectorStoreFiles.filter(
        (vsf) => vsf.vectorStoreId !== vectorStoreId && filesToAssign.includes(vsf.fileId)
      );

      // Xóa các bản ghi liên kết cũ (nếu file đã được gán vào vector store khác)
      if (vectorStoreFilesToRemove.length > 0) {
        await this.vectorStoreFileRepository.remove(vectorStoreFilesToRemove);
      }

      // Lọc ra các file chưa được gán vào vector store hiện tại
      const filesToCreateAssignment = filesToAssign.filter((fileId) => {
        return !existingVectorStoreFiles.some(
          (vsf) => vsf.fileId === fileId && vsf.vectorStoreId === vectorStoreId
        );
      });

      // Tạo các bản ghi liên kết mới cho các file chưa được gán
      if (filesToCreateAssignment.length > 0) {
        const vectorStoreFiles = filesToCreateAssignment.map((fileId) => ({
          vectorStoreId,
          fileId,
        }));

        // Lưu vào database
        await this.vectorStoreFileRepository.save(vectorStoreFiles);
      }

      // Lấy danh sách file đã được gán
      const filesToProcess = files.filter((file) =>
        filesToAssign.includes(file.id),
      );

      // Tạo file ID ngay lập tức và xử lý RAG API async
      const processedFileDetails: ProcessedFileDetailDto[] = [];
      const skippedFiles: string[] = [];

      this.logger.log(`Bắt đầu tạo file ID cho ${filesToProcess.length} file và gửi đến RAG API async`);

      for (const file of filesToProcess) {
        try {
          this.logger.log(`Đang tạo file ID cho file ${file.id} với storageKey ${file.storageKey}`);

          // Tạo file ID ngay lập tức bằng UUID
          const fileId = `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

          // Cập nhật fileId trong database ngay lập tức
          file.fileId = fileId;
          await this.knowledgeFileRepository.save(file);

          // Thêm file đã tạo ID thành công vào danh sách
          processedFileDetails.push({
            id: file.id,
            openAiFileId: file.fileId,
          });

          this.logger.log(`Đã tạo file ID ${file.id} thành công: ${file.fileId}`);

          // Gọi RAG API async (fire-and-forget) - không chờ kết quả
          this.processFileWithRagApiAsync(
            file.storageKey,
            dto.chunkSize || 2000,
            dto.chunkOverlap || 100,
            vectorStoreId,
            file.id,
            fileId
          ).catch(error => {
            this.logger.warn(
              `Lỗi khi xử lý file ${file.id} với RAG API trong background: ${error.message}`,
              error.stack,
            );
          });

        } catch (error) {
          this.logger.warn(
            `Lỗi khi tạo file ID cho file ${file.id}: ${error.message}`,
            error.stack,
          );
          skippedFiles.push(file.id);
        }
      }

      const successfullyProcessedCount = processedFileDetails.length;

      const result: AssignFilesResponseDto = {
        success: true,
        message: `Đã tạo file ID cho ${successfullyProcessedCount} file thành công. Đã gán ${successfullyProcessedCount} file vào vector store. RAG API đang xử lý trong background.`,
        processedFiles: successfullyProcessedCount,
        processedFileDetails,
        skippedFiles,
      };

      return result;
    } catch (error) {
      this.logger.error(
        `Error assigning files to vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        // Re-throw existing AppException
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_ASSIGN_FILES_ERROR,
        `Lỗi khi gán file vào vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xóa các file khỏi vector store
   * @param vectorStoreId ID của vector store
   * @param fileIds Danh sách ID của các file cần xóa
   * @param userId ID của người dùng
   * @returns Thông tin về việc xóa file thành công
   */
  @Transactional()
  async removeFilesFromVectorStore(
    vectorStoreId: string,
    fileIds: string[],
    userId: number,
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Kiểm tra các file có tồn tại trong vector store không
      const vectorStoreFiles = await this.vectorStoreFileRepository.find({
        where: { vectorStoreId, fileId: In(fileIds) },
      });

      if (vectorStoreFiles.length === 0) {
        return {
          success: true,
          message: 'Không có file nào cần xóa khỏi vector store.',
        };
      }

      // Lấy thông tin các file để xóa khỏi OpenAI
      const filesToRemove = await this.knowledgeFileRepository.find({
        where: { id: In(vectorStoreFiles.map((vsf) => vsf.fileId)) },
      });

      // Xóa các bản ghi liên kết
      await this.vectorStoreFileRepository.remove(vectorStoreFiles);

      // Không cần xóa các file khỏi vector store trên OpenAI nữa
      this.logger.log(`Bỏ qua bước xóa file khỏi OpenAI, chỉ xóa liên kết trong database`);

      return {
        success: true,
        message: `Đã xóa ${vectorStoreFiles.length} file khỏi vector store thành công.`,
      };
    } catch (error) {
      this.logger.error(
        `Error removing files from vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_REMOVE_FILES_ERROR,
        `Lỗi khi xóa file khỏi vector store: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin vector store
   * @param vectorStoreId ID của vector store cần cập nhật
   * @param dto Thông tin cập nhật
   * @param userId ID của người dùng
   * @returns Thông tin vector store đã cập nhật
   */
  @Transactional()
  async updateVectorStore(
    vectorStoreId: string,
    dto: UpdateVectorStoreDto,
    userId: number,
  ): Promise<VectorStoreResponseDto> {
    try {
      this.logger.log(`Bắt đầu cập nhật vector store ${vectorStoreId} cho user ${userId}`);

      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Không cần cập nhật thông tin vector store trên FastAPI nữa
      this.logger.log(`Bỏ qua bước cập nhật vector store trên FastAPI, chỉ cập nhật trong database`);

      // Cập nhật thông tin trong database
      vectorStore.name = dto.name;
      if (dto.storage !== undefined) {
        vectorStore.storage = dto.storage;
      }
      vectorStore.updateAt = Date.now();

      // Lưu vào database
      await this.vectorStoreRepository.save(vectorStore);

      // Đếm số lượng file và agent trong vector store
      const fileCount =
        await this.vectorStoreRepository.countFilesByVectorStoreId(
          vectorStoreId,
        );
      const agentCount =
        await this.vectorStoreRepository.countAgentsByVectorStoreId(
          vectorStoreId,
        );

      // Trả về thông tin vector store đã cập nhật
      return plainToInstance(
        VectorStoreResponseDto,
        {
          storeId: vectorStore.id,
          storeName: vectorStore.name,
          size: vectorStore.storage,
          agents: agentCount,
          files: fileCount,
          createdAt: vectorStore.createdAt,
          updatedAt: vectorStore.updateAt,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(
        `Error updating vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_UPDATE_ERROR,
        `Lỗi khi cập nhật vector store: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một vector store
   * @param vectorStoreId ID của vector store
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của vector store
   */
  async getVectorStoreDetail(
    vectorStoreId: string,
    userId: number,
  ): Promise<VectorStoreResponseDto> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Đếm số lượng file và agent trong vector store
      const fileCount =
        await this.vectorStoreRepository.countFilesByVectorStoreId(
          vectorStoreId,
        );
      const agentCount =
        await this.vectorStoreRepository.countAgentsByVectorStoreId(
          vectorStoreId,
        );

      // Trả về thông tin vector store
      return plainToInstance(
        VectorStoreResponseDto,
        {
          storeId: vectorStore.id,
          storeName: vectorStore.name,
          size: vectorStore.storage,
          agents: agentCount,
          files: fileCount,
          createdAt: vectorStore.createdAt,
          updatedAt: vectorStore.updateAt,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(
        `Error getting vector store detail: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
        `Lỗi khi lấy thông tin chi tiết vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xóa một vector store
   * @param vectorStoreId ID của vector store
   * @param userId ID của người dùng
   */
  @Transactional()
  async deleteVectorStore(
    vectorStoreId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra vector store có tồn tại và thuộc về người dùng không
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndUserId(
        vectorStoreId,
        userId,
      );

      // Xác thực vector store tồn tại
      this.validationHelper.validateVectorStoreExists(
        vectorStore,
        vectorStoreId,
      );

      // Không cần xóa vector store trên OpenAI nữa
      this.logger.log(`Bỏ qua bước xóa vector store trên OpenAI, chỉ xóa trong database`);

      // Xóa các bản ghi liên kết với file
      await this.vectorStoreFileRepository.delete({ vectorStoreId });

      // Xóa vector store
      await this.vectorStoreRepository.delete({ id: vectorStoreId });
    } catch (error) {
      this.logger.error(
        `Error deleting vector store: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi xóa vector store: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý file với RAG API trong background (async)
   * @param storageKey Storage key của file
   * @param chunkSize Kích thước chunk
   * @param chunkOverlap Độ chồng lấp chunk
   * @param vectorStoreId ID của vector store
   * @param fileId ID của file trong database
   * @param generatedFileId File ID đã tạo
   */
  private async processFileWithRagApiAsync(
    storageKey: string,
    chunkSize: number,
    chunkOverlap: number,
    vectorStoreId: string,
    fileId: string,
    generatedFileId: string,
  ): Promise<void> {
    try {
      this.logger.log(`Bắt đầu xử lý file ${fileId} với RAG API trong background`);

      // Gọi RAG API để xử lý file và gán vào vector store
      const processResult = await this.ragFileProcessingService.processFileFromS3Key(
        storageKey,
        chunkSize,
        chunkOverlap,
        vectorStoreId
      );

      this.logger.log(`File ${fileId} đã được xử lý thành công với RAG API: ${processResult.file_id}`);

      // Cập nhật fileId trong database với kết quả từ RAG API (nếu khác)
      if (processResult.file_id !== generatedFileId) {
        const file = await this.knowledgeFileRepository.findOne({
          where: { id: fileId }
        });
        if (file) {
          file.fileId = processResult.file_id;
          await this.knowledgeFileRepository.save(file);
          this.logger.log(`Đã cập nhật file ID từ ${generatedFileId} thành ${processResult.file_id}`);
        }
      }

    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý file ${fileId} với RAG API trong background: ${error.message}`,
        error.stack,
      );
      // Không throw error vì đây là background process
    }
  }
}
