import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { AxiosRequestConfig } from 'axios';
import { backOff } from 'exponential-backoff';
import { S3Service } from '../s3.service';
/**
 * Interface for RAG API file processing request
 */
export interface RagFileProcessRequest {
  chunk_overlap: number;
  chunk_size: number;
  url: string;
  vector_store_id?: string;
}

/**
 * Interface for RAG API file processing response
 */
export interface RagFileProcessResponse {
  chunk_overlap: number;
  chunk_size: number;
  file_id: string;
  filename: string;
  message: string;
  status: string;
  vector_store_id?: string;
  vector_store_name?: string;
}

/**
 * Interface for RAG API file progress response
 */
export interface RagFileProgressResponse {
  file_id: string;
  progress: number;
  status: string;
  message?: string;
  error?: string;
}

/**
 * Service chuyên xử lý tập tin sử dụng RAG API
 */
@Injectable()
export class RagFileProcessingService {
  private readonly logger = new Logger(RagFileProcessingService.name);
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly s3Service: S3Service,
  ) {
    this.apiKey = this.configService.get<string>('RAG_API', '');
    this.baseUrl = this.configService.get<string>('FAST_API_URL', 'http://localhost:8000/');

    if (!this.apiKey) {
      this.logger.warn('RAG API key is not configured');
    }

    if (!this.baseUrl) {
      this.logger.warn('Fast API URL is not configured');
    }
  }

  /**
   * Tạo config cho request API với X-API-Key header
   * @param timeoutMs Timeout cho request (ms)
   * @returns AxiosRequestConfig
   */
  private createRequestConfig(timeoutMs: number = 10000): AxiosRequestConfig {
    return {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
      },
      timeout: timeoutMs,
    };
  }

  /**
   * Gửi file đến RAG API để lấy file ID ngay lập tức (fire-and-forget)
   * @param storageKey S3 key của tập tin
   * @param chunkSize Kích thước của mỗi đoạn văn bản
   * @param chunkOverlap Độ chồng lấp giữa các đoạn
   * @param vectorStoreId ID của vector store để lưu trữ các đoạn (tùy chọn)
   * @returns File ID từ RAG API
   * @throws AppException nếu có lỗi khi gửi request
   */
  async processFileFromS3Key(
    storageKey: string,
    chunkSize: number = 2000,
    chunkOverlap: number = 100,
    vectorStoreId?: string,
  ): Promise<RagFileProcessResponse> {
    try {
      this.logger.log(`Bắt đầu xử lý file từ S3 key: ${storageKey}`);

      // Gọi API để xử lý file từ S3 key trực tiếp
      const endpoint = 'api/files/process-url';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      // Chuyển đổi storage key thành URL CDN đầy đủ
      const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
      const fullCdnUrl = `${cdnUrl}/${storageKey}`;

      this.logger.log(`Đang gửi URL đến RAG API: ${fullCdnUrl}`);

      const data: RagFileProcessRequest = {
        url: fullCdnUrl, // Sử dụng URL CDN đầy đủ thay vì chỉ storage key
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
      };

      // Thêm vector_store_id vào request nếu được cung cấp
      if (vectorStoreId) {
        data.vector_store_id = vectorStoreId;
        this.logger.log(`Đang gửi file đến vector store: ${vectorStoreId}`);
      }

      // Tạo file ID tạm thời để trả về ngay
      const tempFileId = `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // Gửi request async (fire-and-forget) - không đợi response
      const config = this.createRequestConfig(30000); // 30 giây timeout cho background processing

      // Fire-and-forget: gửi request nhưng không đợi kết quả
      this.httpService.post<RagFileProcessResponse>(fullUrl, data, config)
        .subscribe({
          next: (response) => {
            this.logger.log(`RAG API đã xử lý file thành công: ${response.data.file_id}`);
            // TODO: Có thể update database với file ID thật từ RAG API ở đây
          },
          error: (error) => {
            this.logger.error(`Lỗi khi xử lý file với RAG API: ${error.message}`);
          }
        });

      this.logger.log(`Đã gửi file đến RAG API, trả về file ID tạm thời: ${tempFileId}`);

      // Trả về response ngay với file ID tạm thời
      return {
        file_id: tempFileId,
        filename: storageKey.split('/').pop() || 'unknown',
        status: 'processing',
        message: 'File đã được gửi đến RAG API để xử lý',
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
        vector_store_id: vectorStoreId,
        vector_store_name: vectorStoreId ? `Vector Store ${vectorStoreId}` : undefined,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý file từ S3 key: ${error.message}`,
        error.stack,
      );

      // Xử lý lỗi từ RAG API
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi xử lý tập tin từ S3: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý tập tin từ URL S3
   * @param url URL của tập tin trên S3
   * @param chunkSize Kích thước của mỗi đoạn văn bản
   * @param chunkOverlap Độ chồng lấp giữa các đoạn
   * @param vectorStoreId ID của vector store để lưu trữ các đoạn (tùy chọn)
   * @returns Thông tin về quá trình xử lý tập tin
   * @throws AppException nếu có lỗi khi xử lý tập tin
   */
  async processFileFromUrl(
    url: string,
    chunkSize: number = 2000,
    chunkOverlap: number = 100,
    vectorStoreId?: string,
  ): Promise<RagFileProcessResponse> {
    try {
      this.logger.log(`Xử lý tập tin từ URL: ${url}`);

      const endpoint = 'api/files/process-url';
      const fullUrl = `${this.baseUrl}${endpoint}`;

      // Kiểm tra xem URL đã có tiền tố CDN chưa
      let processUrl = url;
      if (!url.startsWith('http')) {
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        processUrl = `${cdnUrl}/${url}`;
        this.logger.log(`Đã chuyển đổi URL thành: ${processUrl}`);
      }

      const data: RagFileProcessRequest = {
        url: processUrl,
        chunk_size: chunkSize,
        chunk_overlap: chunkOverlap,
      };

      // Thêm vector_store_id vào request nếu được cung cấp
      if (vectorStoreId) {
        data.vector_store_id = vectorStoreId;
        this.logger.log(`Đang gửi file đến vector store: ${vectorStoreId}`);
      }

      const config = this.createRequestConfig();

      // Thiết lập timeout cho request
      const response = await firstValueFrom(
        this.httpService.post<RagFileProcessResponse>(fullUrl, data, config),
      );

      this.logger.log(`Bắt đầu xử lý tập tin: ${response.data.file_id}`);
      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xử lý tập tin từ URL: ${error.message}`,
        error.stack,
      );

      if (error.response) {
        this.logger.error(`Mã lỗi: ${error.response.status}`);
        this.logger.error(`Dữ liệu lỗi: ${JSON.stringify(error.response.data)}`);

        if (error.response.status === 401 || error.response.status === 403) {
          throw new AppException(
            ErrorCode.TOKEN_NOT_FOUND,
            'Không có quyền truy cập RAG API',
          );
        }
      }

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi xử lý tập tin: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra tiến độ xử lý tập tin
   * @param fileId ID của tập tin đang xử lý
   * @returns Thông tin về tiến độ xử lý tập tin
   * @throws AppException nếu có lỗi khi kiểm tra tiến độ
   */
  async checkFileProgress(fileId: string): Promise<RagFileProgressResponse> {
    try {
      this.logger.log(`Kiểm tra tiến độ xử lý tập tin: ${fileId}`);

      const endpoint = `api/files/${fileId}/progress`;
      const fullUrl = `${this.baseUrl}${endpoint}`;

      const config = this.createRequestConfig();

      const response = await firstValueFrom(
        this.httpService.get<RagFileProgressResponse>(fullUrl, config),
      );

      this.logger.log(`Tiến độ xử lý tập tin: ${response.data.status}, ${response.data.progress}%`);
      return response.data;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra tiến độ xử lý tập tin: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi kiểm tra tiến độ xử lý tập tin: ${error.message}`,
      );
    }
  }

  /**
   * Chờ cho đến khi tập tin được xử lý hoàn tất
   * @param fileId ID của tập tin đang xử lý
   * @param maxAttempts Số lần thử tối đa
   * @param initialDelayMs Thời gian chờ ban đầu (ms)
   * @returns Thông tin về tiến độ xử lý tập tin khi hoàn tất
   * @throws AppException nếu có lỗi khi chờ xử lý tập tin
   */
  async waitForFileProcessing(
    fileId: string,
    maxAttempts: number = 30,
    initialDelayMs: number = 2000,
  ): Promise<RagFileProgressResponse> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          const progress = await this.checkFileProgress(fileId);

          if (progress.status === 'completed') {
            return progress;
          }

          if (progress.status === 'failed' || progress.status === 'error') {
            throw new Error(`Xử lý tập tin thất bại: ${progress.error || 'Lỗi không xác định'}`);
          }

          // Nếu chưa hoàn tất, ném lỗi để tiếp tục thử lại
          throw new Error('Tập tin đang được xử lý');
        },
        {
          numOfAttempts: maxAttempts,
          startingDelay: initialDelayMs,
          timeMultiple: 1.5,
          delayFirstAttempt: false,
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Lỗi khi chờ xử lý tập tin: ${error.message}`,
        error.stack,
      );

      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Lỗi khi chờ xử lý tập tin: ${error.message}`,
      );
    }
  }
}
