import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { S3Service } from './s3.service';
import { OpenAiService } from './ai/openai.service';
import { AnthropicService } from './ai/anthropic.service';
import { GoogleAIService } from './ai/google_ai.service';
import { DeepSeekService } from './ai/deepseek.service';
import { MetaAIService } from './ai/metaai.service';
import { XAIService } from './ai/xai.service';
import { RagFileProcessingService } from './ai/rag-file-processing.service';
import { AiProviderHelper } from './ai/helpers/ai-provider.helper';
import { CdnService } from './cdn.service';
import { RedisService } from '@shared/services/redis.service';
import { RecaptchaService } from '@shared/services/recaptcha.service';
import { PdfService } from '@shared/services/pdf.service';
import { PdfEditService } from '@/shared/services/pdf/pdf-edit.service';
import { EncryptionService } from '@shared/services/encryption.service';
import { SmsModule } from './sms/sms.module';
import { SepayHubModule } from '@shared/services/sepay-hub';
import { TelegramModule } from './telegram/telegram.module';
import { ZaloModule } from './zalo/zalo.module';
import { GoogleApiModule } from '@shared/services/google';
import { AuthenticatorModule } from './authenticator/authenticator.module';
import { MarketingAiModule } from '@shared/services/marketing-ai';
import { FacebookModule } from './facebook/facebook.module';
import { ConfigModule } from '@nestjs/config';
import { ApiKeyEncryptionHelper } from '@modules/model-training/helpers/api-key-encryption.helper';
import { RagApiInterceptor } from '@shared/interceptors/rag-api.interceptor';

@Global()
@Module({
  imports: [
    HttpModule,
    SmsModule,
    SepayHubModule,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    FacebookModule,
    ConfigModule,
  ],
  providers: [
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    MetaAIService,
    XAIService,
    RagFileProcessingService,
    AiProviderHelper,
    CdnService,
    RedisService,
    RecaptchaService,
    PdfService,
    PdfEditService,
    EncryptionService,
    ApiKeyEncryptionHelper,
    RagApiInterceptor,
  ],
  exports: [
    S3Service,
    OpenAiService,
    AnthropicService,
    GoogleAIService,
    DeepSeekService,
    MetaAIService,
    XAIService,
    RagFileProcessingService,
    AiProviderHelper,
    CdnService,
    RedisService,
    RecaptchaService,
    PdfService,
    PdfEditService,
    EncryptionService,
    ApiKeyEncryptionHelper,
    SepayHubModule,
    TelegramModule,
    ZaloModule,
    GoogleApiModule,
    AuthenticatorModule,
    MarketingAiModule,
    FacebookModule,
    RagApiInterceptor,
  ],
})
export class ServicesModule {}
