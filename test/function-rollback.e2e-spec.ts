import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserFunction, UserFunctionVersion } from '../src/modules/data/functions/entities';
import { FunctionStatusEnum } from '../src/modules/data/functions/constants';
import { JwtService } from '@nestjs/jwt';

describe('Function Rollback (e2e)', () => {
  let app: INestApplication;
  let userFunctionRepository: Repository<UserFunction>;
  let userVersionRepository: Repository<UserFunctionVersion>;
  let jwtService: JwtService;
  
  // Test data
  const testUserId = 1;
  const testFunctionId = 'test-function-id';
  const testVersionId = 'test-version-id';
  const testAdminFunctionId = 'test-admin-function-id';
  
  // JWT token for authentication
  let authToken: string;
  
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
    
    userFunctionRepository = moduleFixture.get<Repository<UserFunction>>(
      getRepositoryToken(UserFunction),
    );
    userVersionRepository = moduleFixture.get<Repository<UserFunctionVersion>>(
      getRepositoryToken(UserFunctionVersion),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);
    
    // Create JWT token for authentication
    authToken = jwtService.sign({ id: testUserId, type: 'user' });
    
    // Setup test data
    await setupTestData();
  });
  
  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await app.close();
  });
  
  async function setupTestData() {
    // Create test user function
    const userFunction = userFunctionRepository.create({
      id: testFunctionId,
      name: 'Test Function',
      description: 'Test Description',
      originalId: testAdminFunctionId,
      versionDefault: testVersionId,
      status: FunctionStatusEnum.APPROVED,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      hasUpdate: false,
    });
    await userFunctionRepository.save(userFunction);
    
    // Create test user function version
    const userVersion = userVersionRepository.create({
      id: testVersionId,
      userId: testUserId,
      originalFunctionId: testFunctionId,
      versionNumber: 1,
      functionName: 'testFunction_u1',
      functionDescription: 'Test Description',
      parameters: { type: 'object', properties: {} },
      changeDescription: 'Initial version',
      status: FunctionStatusEnum.APPROVED,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      edited: true,
    });
    await userVersionRepository.save(userVersion);
  }
  
  async function cleanupTestData() {
    await userVersionRepository.delete({ id: testVersionId });
    await userFunctionRepository.delete({ id: testFunctionId });
  }
  
  describe('/user/functions/:functionId/versions/:versionId/rollback (POST)', () => {
    it('should rollback a user function version to admin version', async () => {
      return request(app.getHttpServer())
        .post(`/user/functions/${testFunctionId}/versions/${testVersionId}/rollback`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(200)
        .then(response => {
          expect(response.body.success).toBe(true);
          expect(response.body.message).toContain('Rollback Function về phiên bản gốc thành công');
          expect(response.body.data).toBeDefined();
          expect(response.body.data.version).toBeDefined();
          
          // Verify that the version was updated in the database
          return userVersionRepository.findOne({ where: { id: testVersionId } });
        })
        .then(updatedVersion => {
          expect(updatedVersion).toBeDefined();
          expect(updatedVersion.edited).toBe(false);
        });
    });
    
    it('should return 404 when function not found', async () => {
      return request(app.getHttpServer())
        .post(`/user/functions/non-existent-id/versions/${testVersionId}/rollback`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(404);
    });
    
    it('should return 404 when version not found', async () => {
      return request(app.getHttpServer())
        .post(`/user/functions/${testFunctionId}/versions/non-existent-id/rollback`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(404);
    });
    
    it('should return 401 when not authenticated', async () => {
      return request(app.getHttpServer())
        .post(`/user/functions/${testFunctionId}/versions/${testVersionId}/rollback`)
        .send({})
        .expect(401);
    });
  });
});
